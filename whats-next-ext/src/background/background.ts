// Define interfaces for type safety
interface PageContext {
  title: string;
  h1: string;
}

interface MessagePayload {
  type: 'getSuggestion';
  text: string;
  pageContext: PageContext;
}

chrome.runtime.onMessage.addListener((request: MessagePayload, _sender, sendResponse) => {
  if (request.type === 'getSuggestion') {
    const YOUR_GEMINI_API_KEY = "AIzaSyAYFyMAWmFQwQhCV16zaNVEX5T60yGCD2s"; // Store securely in production
    const { text, pageContext } = request;

    // Create a detailed prompt using the page context for better suggestions
    const prompt = `You are an intelligent autocomplete assistant. Complete the following text naturally and contextually.

Context:
- Page: "${pageContext.title}"
- Section: "${pageContext.h1}"

Text to complete: "${text}"

Rules:
- Only provide the completion, no explanations
- Keep it concise (1-10 words max)
- Match the writing style and tone
- Be contextually relevant to the page content
- Don't repeat the input text

Completion:`;

    fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${YOUR_GEMINI_API_KEY}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: {
            temperature: 0.3,
            maxOutputTokens: 32,
            stopSequences: ["\n", ".", "!", "?"],
        },
      }),
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
          let completion = data.candidates[0].content.parts[0].text.trim();

          // Clean up the completion
          completion = completion.replace(/^["']|["']$/g, ''); // Remove quotes
          completion = completion.split('\n')[0]; // Take only first line

          if (completion && completion.length > 0) {
            // Ensure proper spacing
            const needsSpace = !text.endsWith(' ') && !completion.startsWith(' ');
            const fullSuggestion = text + (needsSpace ? ' ' : '') + completion;
            sendResponse({ suggestion: fullSuggestion });
          } else {
            sendResponse({});
          }
        } else {
          console.log('No valid completion in response:', data);
          sendResponse({});
        }
      })
      .catch(error => {
        console.error('Error fetching from Gemini API:', error);
        sendResponse({}); // Send empty response on error
      });

    return true; // Indicates an asynchronous response
  }
});
