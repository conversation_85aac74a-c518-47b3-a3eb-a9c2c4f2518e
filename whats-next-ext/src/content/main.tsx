import './autocomplete.css';

// Debug logging function
function debugLog(message: string, data?: any) {
  console.log(`[AI Autocomplete Content] ${message}`, data || '');
}

// Check if we're in debug mode (can be toggled from console)
(window as any).aiAutocompleteDebug = {
  enabled: false,
  toggle: () => {
    (window as any).aiAutocompleteDebug.enabled = !(window as any).aiAutocompleteDebug.enabled;
    document.body.classList.toggle('ai-autocomplete-debug', (window as any).aiAutocompleteDebug.enabled);
    debugLog('Debug mode:', (window as any).aiAutocompleteDebug.enabled ? 'ON' : 'OFF');
  },
  status: () => {
    const manager = (window as any).aiAutocompleteManager;
    if (manager) {
      debugLog('Extension Status:', {
        inputsFound: document.querySelectorAll('input, textarea').length,
        activeInput: !!manager.activeInput,
        currentSuggestion: manager.suggestion
      });
    }
  }
};

class AutocompleteManager {
  private processedInputs = new WeakSet<HTMLElement>();
  private activeInput: HTMLInputElement | HTMLTextAreaElement | null = null;
  private ghostOverlay: HTMLDivElement | null = null;
  private suggestion = '';
  private debounceTimer: number | null = null;
  private debugIndicator: HTMLDivElement | null = null;

  constructor() {
    debugLog('AutocompleteManager initializing...');
    this.init();
    this.createDebugIndicator();
  }

  private init() {
    debugLog('Initializing autocomplete manager');

    // Process existing inputs
    this.processExistingInputs();

    // Watch for new inputs being added to the DOM
    this.observeNewInputs();

    // Handle focus changes
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));

    // Handle scroll and resize to keep overlay positioned
    window.addEventListener('scroll', this.handleScroll.bind(this), true);
    window.addEventListener('resize', this.handleResize.bind(this));

    debugLog('Autocomplete manager initialized successfully');
  }

  private processExistingInputs() {
    const inputs = document.querySelectorAll<HTMLInputElement | HTMLTextAreaElement | HTMLElement>(
      'input[type="text"], input[type="email"], input[type="search"], input:not([type]), textarea, [contenteditable="true"], [contenteditable=""], .ProseMirror, .ql-editor, .CodeMirror, .ace_editor'
    );

    debugLog(`Found ${inputs.length} existing inputs to process`);
    inputs.forEach(input => this.processInput(input as any));
  }

  private observeNewInputs() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // Check if the added node itself is an input
            if (this.isValidInput(element)) {
              this.processInput(element as HTMLInputElement | HTMLTextAreaElement);
            }

            // Check for inputs within the added node
            const inputs = element.querySelectorAll<HTMLInputElement | HTMLTextAreaElement>(
              'input[type="text"], input[type="email"], input[type="search"], input:not([type]), textarea'
            );
            inputs.forEach(input => this.processInput(input));
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private isValidInput(element: Element): boolean {
    if (element.tagName === 'TEXTAREA') return true;
    if (element.tagName === 'INPUT') {
      const input = element as HTMLInputElement;
      const type = input.type.toLowerCase();
      return type === 'text' || type === 'email' || type === 'search' || !input.hasAttribute('type');
    }
    return false;
  }

  private processInput(input: HTMLInputElement | HTMLTextAreaElement) {
    if (this.processedInputs.has(input)) return;

    // Skip inputs that shouldn't have autocomplete
    if (input.disabled || input.readOnly) {
      debugLog('Skipping disabled/readonly input');
      return;
    }
    if (input.type === 'password' || input.type === 'hidden') {
      debugLog('Skipping password/hidden input');
      return;
    }

    debugLog('Processing input:', {
      tagName: input.tagName,
      type: input.type,
      id: input.id,
      className: input.className
    });

    this.processedInputs.add(input);

    // Add event listeners
    input.addEventListener('input', (e) => this.handleInput(e));
    input.addEventListener('keydown', (e) => this.handleKeyDown(e as KeyboardEvent));
  }

  private handleFocusIn(e: FocusEvent) {
    const target = e.target as HTMLElement;
    debugLog('Focus in event:', { tagName: target.tagName, id: target.id, className: target.className });

    if (this.isValidInput(target) && this.processedInputs.has(target)) {
      debugLog('Setting active input');
      this.activeInput = target as HTMLInputElement | HTMLTextAreaElement;
      this.createGhostOverlay();
    }
  }

  private handleFocusOut(_e: FocusEvent) {
    if (this.activeInput) {
      this.removeGhostOverlay();
      this.activeInput = null;
      this.suggestion = '';
    }
  }

  private handleScroll() {
    if (this.ghostOverlay && this.activeInput) {
      this.updateGhostOverlayPosition();
    }
  }

  private handleResize() {
    if (this.ghostOverlay && this.activeInput) {
      this.updateGhostOverlayPosition();
    }
  }

  private createGhostOverlay() {
    if (!this.activeInput || this.ghostOverlay) return;

    this.ghostOverlay = document.createElement('div');
    this.ghostOverlay.className = 'ai-autocomplete-ghost-overlay';

    // Position the overlay
    this.updateGhostOverlayPosition();

    document.body.appendChild(this.ghostOverlay);
  }

  private removeGhostOverlay() {
    if (this.ghostOverlay) {
      this.ghostOverlay.remove();
      this.ghostOverlay = null;
    }
  }

  private updateGhostOverlayPosition() {
    if (!this.activeInput || !this.ghostOverlay) return;

    const rect = this.activeInput.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(this.activeInput);

    // Copy styles from the input
    this.ghostOverlay.style.position = 'fixed';
    this.ghostOverlay.style.left = rect.left + 'px';
    this.ghostOverlay.style.top = rect.top + 'px';
    this.ghostOverlay.style.width = rect.width + 'px';
    this.ghostOverlay.style.height = rect.height + 'px';
    this.ghostOverlay.style.font = computedStyle.font;
    this.ghostOverlay.style.fontSize = computedStyle.fontSize;
    this.ghostOverlay.style.fontFamily = computedStyle.fontFamily;
    this.ghostOverlay.style.fontWeight = computedStyle.fontWeight;
    this.ghostOverlay.style.lineHeight = computedStyle.lineHeight;
    this.ghostOverlay.style.letterSpacing = computedStyle.letterSpacing;
    this.ghostOverlay.style.textAlign = computedStyle.textAlign;
    this.ghostOverlay.style.padding = computedStyle.padding;
    this.ghostOverlay.style.paddingLeft = computedStyle.paddingLeft;
    this.ghostOverlay.style.paddingRight = computedStyle.paddingRight;
    this.ghostOverlay.style.paddingTop = computedStyle.paddingTop;
    this.ghostOverlay.style.paddingBottom = computedStyle.paddingBottom;
    this.ghostOverlay.style.border = 'none';
    this.ghostOverlay.style.background = 'transparent';
    this.ghostOverlay.style.pointerEvents = 'none';
    this.ghostOverlay.style.zIndex = '999999';
    this.ghostOverlay.style.color = '#999';
    this.ghostOverlay.style.opacity = '0.6';
    this.ghostOverlay.style.whiteSpace = this.activeInput.tagName === 'TEXTAREA' ? 'pre-wrap' : 'pre';
    this.ghostOverlay.style.overflow = 'hidden';
    this.ghostOverlay.style.boxSizing = computedStyle.boxSizing;
    this.ghostOverlay.style.textIndent = computedStyle.textIndent;
    this.ghostOverlay.style.direction = computedStyle.direction;
  }

  private handleInput(e: Event) {
    const input = e.target as HTMLInputElement | HTMLTextAreaElement;
    const text = input.value;

    debugLog('Input event:', {
      textLength: text.length,
      text: text.substring(0, 50) + (text.length > 50 ? '...' : '')
    });

    this.suggestion = '';
    this.updateGhostText();

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    if (text.trim().length === 0) {
      debugLog('Empty text, skipping suggestion');
      return;
    }

    const wordCount = text.trim().split(/\s+/).filter(Boolean).length;
    debugLog('Word count:', wordCount);

    const fetchSuggestion = () => {
      const pageContext = {
        title: document.title,
        h1: document.querySelector('h1')?.textContent ?? '',
      };

      debugLog('Fetching suggestion...', { pageContext });

      chrome.runtime.sendMessage(
        { type: 'getSuggestion', text, pageContext },
        (response) => {
          if (chrome.runtime.lastError) {
            debugLog('Runtime error:', chrome.runtime.lastError.message);
            return;
          }

          debugLog('Received response from background:', response);
          if (response?.suggestion && this.activeInput === input) {
            this.suggestion = response.suggestion;
            debugLog('Setting suggestion:', this.suggestion);
            this.updateGhostText();
          } else {
            debugLog('No suggestion received or input changed');
          }
        }
      );
    };

    // Trigger immediately for longer text, otherwise debounce
    if (wordCount >= 3 && text.endsWith(' ')) {
      debugLog('Triggering immediate suggestion (word count >= 3 and ends with space)');
      fetchSuggestion();
    } else {
      debugLog('Setting debounce timer for suggestion');
      this.debounceTimer = window.setTimeout(fetchSuggestion, 800);
    }
  }

  private updateGhostText() {
    if (!this.ghostOverlay || !this.activeInput) return;

    const currentText = this.activeInput.value;
    const ghostText = this.suggestion && this.suggestion.toLowerCase().startsWith(currentText.toLowerCase())
      ? this.suggestion.slice(currentText.length)
      : '';

    // Create the display text with current text (invisible) + ghost text
    this.ghostOverlay.innerHTML = `<span style="color: transparent;">${this.escapeHtml(currentText)}</span><span style="color: #999; opacity: 0.6;">${this.escapeHtml(ghostText)}</span>`;

    // Update position in case input moved
    this.updateGhostOverlayPosition();
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  private handleKeyDown(e: KeyboardEvent) {
    if (!this.activeInput || !this.suggestion) return;

    const input = this.activeInput;
    const currentText = input.value;
    const ghostText = this.suggestion.toLowerCase().startsWith(currentText.toLowerCase())
      ? this.suggestion.slice(currentText.length)
      : '';

    if (!ghostText) return;

    const isAtEnd = input.selectionStart === currentText.length && input.selectionEnd === currentText.length;

    if ((e.key === 'Tab' || e.key === 'ArrowRight') && isAtEnd) {
      e.preventDefault();

      const newValue = currentText + ghostText;
      input.value = newValue;

      // Trigger input event to notify the page
      input.dispatchEvent(new Event('input', { bubbles: true }));

      this.suggestion = '';
      this.updateGhostText();
    } else if (e.key === 'Escape') {
      // Dismiss suggestion on Escape
      this.suggestion = '';
      this.updateGhostText();
    }
  }

  // Debug method for popup
  getProcessedInputsCount(): number {
    // Since WeakSet doesn't have a size property, we'll track this differently
    // For now, return a placeholder
    return document.querySelectorAll('input, textarea').length;
  }

  private createDebugIndicator() {
    // Create a small debug indicator in the corner
    this.debugIndicator = document.createElement('div');
    this.debugIndicator.style.position = 'fixed';
    this.debugIndicator.style.top = '10px';
    this.debugIndicator.style.right = '10px';
    this.debugIndicator.style.width = '12px';
    this.debugIndicator.style.height = '12px';
    this.debugIndicator.style.backgroundColor = '#4CAF50';
    this.debugIndicator.style.borderRadius = '50%';
    this.debugIndicator.style.zIndex = '999999';
    this.debugIndicator.style.border = '2px solid white';
    this.debugIndicator.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
    this.debugIndicator.title = 'AI Autocomplete Active';
    this.debugIndicator.style.cursor = 'pointer';

    // Add click handler to show status
    this.debugIndicator.addEventListener('click', () => {
      const inputCount = document.querySelectorAll('input, textarea').length;
      alert(`AI Autocomplete Status:\n✓ Extension Active\n📝 ${inputCount} inputs found\n🎯 Active input: ${this.activeInput ? 'Yes' : 'None'}\n💡 Last suggestion: ${this.suggestion || 'None'}`);
    });

    document.body.appendChild(this.debugIndicator);

    // Remove after 5 seconds to avoid cluttering the page
    setTimeout(() => {
      if (this.debugIndicator && this.debugIndicator.parentNode) {
        this.debugIndicator.parentNode.removeChild(this.debugIndicator);
      }
    }, 5000);
  }
}

// Initialize the autocomplete manager
const manager = new AutocompleteManager();

// Make manager available globally for debugging
(window as any).aiAutocompleteManager = manager;

// Handle messages from popup for debugging
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.type === 'ping') {
    debugLog('Received ping from popup');
    sendResponse({ status: 'active', inputsProcessed: manager.getProcessedInputsCount() });
    return true;
  }
});
